import { useUser } from "@clerk/clerk-expo";
import { useRouter } from "expo-router";
import React, { useState } from "react";
import { ActivityIndicator, StyleSheet, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import ProgressBar from "./ProgressBar";
import InterestSelection from "./steps/InterestSelection";
import ProfileSelection from "./steps/ProfileSelection";
import ScholarIdInput from "./steps/ScholarIdInput";

export default function OnboardingFlow() {
    const { user } = useUser();
    const router = useRouter();
    const [currentStep, setCurrentStep] = useState(0);
    const [formData, setFormData] = useState({
        scholarId: "",
        profileData: null,
        selectedInterests: [],
    });
    const [loading, setLoading] = useState(false);
    const steps = [
        { component: ScholarIdInput, title: "Let's find you" },
        { component: ProfileSelection, title: "Let's find you" },
        { component: InterestSelection, title: "Let's find you" },
    ];

    const handleUpdateData = (key: string, value: any) => {
        setFormData((prev) => ({ ...prev, [key]: value }));
    };

    const CurrentStepComponent = steps[currentStep].component;

    const onSubmit = async () => {
        setLoading(true); // Start loading
        try {
            await user?.update({
                unsafeMetadata: {
                    ...formData,
                    onboardingCompleted: true,
                },
            });

            await user?.reload();

            router.replace("/(tabs)/discover");
        } catch (error) {
            console.error("Error submitting form:", error);
        } finally {
            setLoading(false); // End loading
        }
    };

    return (
        <SafeAreaView style={styles.container} edges={["top"]}>
            <ProgressBar
                currentStep={currentStep}
                totalSteps={steps.length}
                title={steps[currentStep].title}
                onBack={() => setCurrentStep((prev) => Math.max(prev - 1, 0))}
            />
            <View style={styles.content}>
                {loading ? (
                    <View style={styles.loadingContainer}>
                        <ActivityIndicator size="large" color="#0000ff" />
                    </View>
                ) : (
                    <CurrentStepComponent
                        data={formData}
                        updateData={handleUpdateData}
                        onNext={() =>
                            setCurrentStep((prev) =>
                                Math.min(prev + 1, steps.length - 1)
                            )
                        }
                        onBack={() =>
                            setCurrentStep((prev) => Math.max(prev - 1, 0))
                        }
                        onSubmit={onSubmit}
                        isLastStep={currentStep === steps.length - 1}
                        isFirstStep={currentStep === 0}
                    />
                )}
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: "#fff",
    },
    content: {
        flex: 1,
        paddingHorizontal: 20,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
    },
});
