import EmptyState from "@/components/EmptyState";
import PaperCard from "@/components/PaperCard";
import { usePaperStore } from "@/store/paperStore";
import { MaterialIcons } from "@expo/vector-icons";
import React, {
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState,
} from "react";
import { Dimensions, Text, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import TinderCard from "react-tinder-card";

const { width: SCREEN_WIDTH } = Dimensions.get("window");

export default function DiscoverScreen() {
    const insets = useSafeAreaInsets();
    const {
        savePaper,
        rejectPaper,
        getFilteredPapers,
        savedPapers,
        rejectedPapers,
    } = usePaperStore();

    // Memoize filtered papers to prevent unnecessary recalculations
    const filteredPapers = useMemo(
        () => getFilteredPapers(),
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [savedPapers.length, rejectedPapers.length, getFilteredPapers]
    );

    const [currentIndex, setCurrentIndex] = useState(filteredPapers.length - 1);

    // Used for outOfFrame closure
    const currentIndexRef = useRef(currentIndex);

    // Create refs for each card to enable programmatic swiping
    const childRefs = useMemo(
        () =>
            Array(filteredPapers.length)
                .fill(0)
                .map(() => React.createRef<any>()),
        [filteredPapers.length]
    );

    const updateCurrentIndex = useCallback((val: number) => {
        setCurrentIndex(val);
        currentIndexRef.current = val;
    }, []);

    // Reset currentIndex when filtered papers change (e.g., after reset)
    useEffect(() => {
        const newIndex = filteredPapers.length - 1;
        if (
            newIndex >= 0 &&
            (currentIndex < 0 || currentIndex >= filteredPapers.length)
        ) {
            updateCurrentIndex(newIndex);
        }
    }, [filteredPapers.length, currentIndex, updateCurrentIndex]);

    const canSwipe = currentIndex >= 0;

    // Handle swipe events
    const swiped = useCallback(
        (direction: string, nameToDelete: string, index: number) => {
            updateCurrentIndex(index - 1);

            const paper = filteredPapers[index];
            if (paper) {
                if (direction === "left") {
                    rejectPaper(paper.id);
                } else if (direction === "right") {
                    savePaper(paper);
                }
            }
        },
        [filteredPapers, rejectPaper, savePaper, updateCurrentIndex]
    );

    const outOfFrame = useCallback(
        (name: string, idx: number) => {
            currentIndexRef.current >= idx &&
                childRefs[idx].current?.restoreCard();
        },
        [childRefs]
    );

    const swipe = useCallback(
        async (dir: string) => {
            if (canSwipe && currentIndex < filteredPapers.length) {
                await childRefs[currentIndex].current?.swipe(dir);
            }
        },
        [canSwipe, currentIndex, filteredPapers.length, childRefs]
    );

    const handleSave = useCallback(() => swipe("right"), [swipe]);
    const handlePass = useCallback(() => swipe("left"), [swipe]);

    if (filteredPapers.length === 0) {
        return (
            <EmptyState
                title="No more papers to discover"
                message="You've reviewed all available papers! Check back later for new research or adjust your preferences."
                icon={
                    <MaterialIcons
                        name="book-online"
                        size={60}
                        color="#4F46E5"
                    />
                }
            />
        );
    }

    c

    return (
        <View className="flex-1 bg-gray-50" style={{ paddingTop: insets.top }}>
            <View className="pt-15 px-5 pb-5 items-center">
                <Text className="text-2xl font-bold text-gray-900 mb-1">
                    Discover Research
                </Text>
                <Text className="text-base text-gray-600">
                    {currentIndex + 1} papers remaining
                </Text>
            </View>

            <View className="flex-1 items-center justify-center">
                <View
                    style={{
                        width: SCREEN_WIDTH * 0.9,
                        height: SCREEN_WIDTH * 1.2, // Fixed aspect ratio
                        position: "relative",
                    }}
                >
                    {filteredPapers
                        .slice(Math.max(0, currentIndex - 3), currentIndex + 1)
                        .reverse()
                        .map((paper, stackIndex) => {
                            const actualIndex = filteredPapers.findIndex(
                                (p) => p.id === paper.id
                            );
                            const isTopCard = actualIndex === currentIndex;
                            const cardPosition = currentIndex - actualIndex;

                            // Deck-like stacking effect calculations
                            const baseScale = 1;
                            const scaleStep = 0.06; // Each card is 6% smaller for more dramatic effect
                            const scale = Math.max(
                                0.82,
                                baseScale - cardPosition * scaleStep
                            );

                            const baseOffset = 0;
                            const offsetStep = -30; // Larger offset for more dramatic stacking
                            const yOffset =
                                baseOffset + cardPosition * offsetStep;

                            // Add horizontal offset for deck effect
                            const xOffset = cardPosition * 5;

                            // Add slight rotation for more realistic deck effect
                            const rotation = 0 * 1.5; // degrees

                            const baseShadowOpacity = 0.25;
                            const shadowOpacityStep = 0.08;
                            const shadowOpacity = Math.max(
                                0.05,
                                baseShadowOpacity -
                                    cardPosition * shadowOpacityStep
                            );

                            const baseBlurRadius = 15;
                            const blurStep = 4;
                            const shadowBlur = Math.max(
                                4,
                                baseBlurRadius - cardPosition * blurStep
                            );

                            return (
                                <View
                                    key={paper.id}
                                    style={{
                                        position: "absolute",
                                        zIndex: isTopCard
                                            ? 10
                                            : 10 - cardPosition,
                                        transform: [
                                            { scale },
                                            { translateY: yOffset },
                                            { translateX: xOffset },
                                            { rotate: `${rotation}deg` },
                                        ],
                                        opacity: isTopCard ? 1 : 0.95,
                                        shadowColor: "#000",
                                        shadowOffset: {
                                            width: 0,
                                            height: 2 + cardPosition,
                                        },
                                        shadowOpacity,
                                        shadowRadius: shadowBlur,
                                        elevation: 10 - cardPosition, // Android shadow
                                    }}
                                >
                                    <TinderCard
                                        ref={childRefs[actualIndex]}
                                        className="swipe"
                                        onSwipe={(dir) =>
                                            swiped(
                                                dir,
                                                paper.title,
                                                actualIndex
                                            )
                                        }
                                        onCardLeftScreen={() =>
                                            outOfFrame(paper.title, actualIndex)
                                        }
                                        swipeRequirementType="position"
                                        swipeThreshold={SCREEN_WIDTH / 4}
                                        preventSwipe={["up", "down"]}
                                    >
                                        <PaperCard
                                            paper={paper}
                                            onSave={handleSave}
                                            onPass={handlePass}
                                            isActive={isTopCard}
                                            stackPosition={cardPosition}
                                        />
                                    </TinderCard>
                                </View>
                            );
                        })}
                </View>
            </View>

            <View className="py-5 px-5 items-center">
                <Text className="text-sm text-gray-600 text-center">
                    Swipe right to save • Swipe left to pass
                </Text>
            </View>
        </View>
    );
}
