import EmptyState from "@/components/EmptyState";
import PaperListItem from "@/components/PaperListItem";
import { usePaperStore } from "@/store/paperStore";
import { MaterialIcons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { useRouter } from "expo-router";
import React from "react";
import { FlatList, Platform, Text, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

export default function SavedScreen() {
    const insets = useSafeAreaInsets();
    const router = useRouter();
    const { savedPapers, removeSavedPaper } = usePaperStore();

    const handlePaperPress = (paperId: string) => {
        router.push(`/paper/${paperId}` as any);
    };

    const handleRemovePaper = (paperId: string) => {
        if (Platform.OS !== "web") {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        }
        removeSavedPaper(paperId);
    };

    if (savedPapers.length === 0) {
        return (
            <View
                className="flex-1 bg-gradient-to-b from-slate-50 to-white"
                style={{ paddingTop: insets.top }}
            >
                <EmptyState
                    title="No Saved Papers"
                    message="Swipe right on papers you want to save for later."
                    icon={
                        <View className="bg-gradient-to-br from-blue-100 to-purple-100 p-6 rounded-3xl mb-4">
                            <MaterialIcons
                                name="bookmark-added"
                                size={48}
                                color="#6366f1"
                            />
                        </View>
                    }
                />
            </View>
        );
    }

    return (
        <View
            className="flex-1 bg-gradient-to-b from-slate-50 to-white"
            style={{ paddingTop: insets.top }}
        >
            <FlatList
                data={savedPapers}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                    <PaperListItem
                        paper={item}
                        onPress={() => handlePaperPress(item.id)}
                        onRemove={() => handleRemovePaper(item.id)}
                    />
                )}
                className="px-5"
                contentContainerStyle={{ paddingBottom: 20 }}
                ListHeaderComponent={
                    <View className="py-6 mb-2">
                        {/* Modern header with enhanced typography */}
                        <View className="flex-row items-end justify-between mb-4">
                            <View>
                                <Text
                                    className="text-3xl font-black text-slate-900 mb-1"
                                    style={{ letterSpacing: -0.5 }}
                                >
                                    Your Library
                                </Text>
                                <View className="flex-row items-center">
                                    <View className="w-1.5 h-1.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mr-2" />
                                    <Text className="text-base text-slate-600 font-medium">
                                        {savedPapers.length}{" "}
                                        {savedPapers.length === 1
                                            ? "paper"
                                            : "papers"}{" "}
                                        saved
                                    </Text>
                                </View>
                            </View>

                            {/* Decorative element */}
                            <View className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 p-3 rounded-2xl">
                                <MaterialIcons
                                    name="auto-stories"
                                    size={24}
                                    color="#6366f1"
                                />
                            </View>
                        </View>

                        {/* Stats bar */}
                        <View className="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-slate-200/30">
                            <View className="flex-row justify-between">
                                <View className="items-center flex-1">
                                    <Text className="text-lg font-bold text-slate-900">
                                        {savedPapers.length}
                                    </Text>
                                    <Text className="text-xs text-slate-600 font-medium">
                                        Total Papers
                                    </Text>
                                </View>
                                <View className="w-px bg-slate-200" />
                                <View className="items-center flex-1">
                                    <Text className="text-lg font-bold text-slate-900">
                                        {savedPapers
                                            .reduce(
                                                (sum, paper) =>
                                                    sum +
                                                    (paper.citationCount || 0),
                                                0
                                            )
                                            .toLocaleString()}
                                    </Text>
                                    <Text className="text-xs text-slate-600 font-medium">
                                        Total Citations
                                    </Text>
                                </View>
                                <View className="w-px bg-slate-200" />
                                <View className="items-center flex-1">
                                    <Text className="text-lg font-bold text-slate-900">
                                        {
                                            new Set(
                                                savedPapers.flatMap(
                                                    (paper) => paper.tags
                                                )
                                            ).size
                                        }
                                    </Text>
                                    <Text className="text-xs text-slate-600 font-medium">
                                        Unique Topics
                                    </Text>
                                </View>
                            </View>
                        </View>
                    </View>
                }
                showsVerticalScrollIndicator={false}
                ItemSeparatorComponent={() => <View className="h-1" />}
            />
        </View>
    );
}
