import Colors from "@/constants/colors";
import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, Text, View } from "react-native";

interface EmptyStateProps {
    title: string;
    message: string;
    icon?: React.ReactNode;
}

export default function EmptyState({ title, message, icon }: EmptyStateProps) {
    return (
        <View style={styles.container}>
            <View style={styles.iconContainer}>
                {icon || (
                    <MaterialIcons
                        name="bookmark-remove"
                        size={50}
                        color={Colors.textSecondary}
                    />
                )}
            </View>
            <Text style={styles.title}>{title}</Text>
            <Text style={styles.message}>{message}</Text>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        padding: 20,
    },
    iconContainer: {
        marginBottom: 20,
    },
    title: {
        fontSize: 20,
        fontWeight: "bold",
        color: Colors.text,
        marginBottom: 10,
        textAlign: "center",
    },
    message: {
        fontSize: 16,
        color: Colors.textSecondary,
        textAlign: "center",
        maxWidth: 300,
    },
});
