import { Tabs } from "expo-router";
import React from "react";

const TabsLayout = () => {
    // const { isSignedIn } = useAuth();
    // const router = useRouter();

    // useEffect(() => {
    //     if (!isSignedIn) {
    //         router.replace("/auth/login");
    //     }
    // }, [isSignedIn, router]);

    // if (!isSignedIn) {
    //     return null; // Don't render tabs if not signed in
    // }

    return (
        <Tabs
            screenOptions={{
                headerShown: false,
                tabBarStyle: {
                    backgroundColor: "#ffffff",
                    borderTopWidth: 1,
                    borderTopColor: "#e5e5e5",
                },
                tabBarActiveTintColor: "#000000",
                tabBarInactiveTintColor: "#666666",
            }}
        >
            <Tabs.Screen
                name="home"
                options={{
                    title: "Home",
                }}
            />
            <Tabs.Screen
                name="saved"
                options={{
                    title: "Liked",
                }}
            />
            <Tabs.Screen
                name="discover"
                options={{
                    title: "Match",
                }}
            />

            <Tabs.Screen
                name="premium"
                options={{
                    title: "Premium",
                }}
            />
            <Tabs.Screen
                name="profile"
                options={{
                    title: "Profile",
                }}
            />
        </Tabs>
    );
};

export default TabsLayout;
