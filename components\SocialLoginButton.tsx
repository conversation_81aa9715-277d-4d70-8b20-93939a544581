import { images } from "@/constants/images";
import { useSSO, useUser } from "@clerk/clerk-expo";
import { Ionicons } from "@expo/vector-icons";
import * as Linking from "expo-linking";
import React, { useState } from "react";
import {
    ActivityIndicator,
    Image,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from "react-native";

const SocialLoginButton = ({
    strategy,
}: {
    strategy: "linkedin_oidc" | "google" | "apple";
}) => {
    const { startSSOFlow } = useSSO();
    const { user } = useUser();
    const [isLoading, setIsLoading] = useState(false);

    const buttonText = () => {
        if (isLoading) {
            return (
                <ActivityIndicator
                    size="small"
                    color="#6366F1"
                    style={{ marginLeft: 10 }}
                />
            );
        }

        if (strategy === "linkedin_oidc") {
            return "Continue with LinkedIn";
        } else if (strategy === "google") {
            return "Continue with Google";
        } else if (strategy === "apple") {
            return "Continue with Apple";
        }
    };

    const buttonIcon = () => {
        if (strategy === "linkedin_oidc") {
            return <Ionicons name="logo-linkedin" size={22} color="#FFFFFF" />;
        } else if (strategy === "google") {
            return (
                <Image
                    source={images.googleIcon}
                    style={{ width: 22, height: 22 }}
                />
            );
        } else if (strategy === "apple") {
            return (
                <Image
                    source={images.appleIcon}
                    style={{ width: 22, height: 22, tintColor: "#000000" }}
                />
            );
        }
    };

    const buttonStyle = () => {
        if (strategy === "linkedin_oidc") {
            return [styles.container, styles.facebookButton];
        } else if (strategy === "google") {
            return [styles.container, styles.googleButton];
        } else if (strategy === "apple") {
            return [styles.container, styles.appleButton];
        }
        return [styles.container];
    };

    const textColor = () => {
        if (strategy === "linkedin_oidc") {
            return "#FFFFFF";
        }
        return "#000000";
    };

    const onSocialLoginPress = React.useCallback(async () => {
        try {
            setIsLoading(true);

            const getStrategy = () => {
                if (strategy === "linkedin_oidc") {
                    return "oauth_linkedin_oidc";
                } else if (strategy === "google") {
                    return "oauth_google";
                } else if (strategy === "apple") {
                    return "oauth_apple";
                }
                return "oauth_google";
            };

            const { createdSessionId, setActive } = await startSSOFlow({
                redirectUrl: Linking.createURL("/dashboard", {
                    scheme: "acavibe",
                }),
                strategy: getStrategy(),
            });

            if (createdSessionId) {
                console.log("Session created", createdSessionId);
                setActive!({ session: createdSessionId });
                await user?.reload();
            }
        } catch (err) {
            console.error(JSON.stringify(err, null, 2));
        } finally {
            setIsLoading(false);
        }
    }, [strategy, startSSOFlow, user]); // Update dependencies to include strategy directly

    return (
        <TouchableOpacity
            style={buttonStyle()}
            onPress={onSocialLoginPress}
            disabled={isLoading}
            activeOpacity={0.8}
        >
            <View style={styles.iconContainer}>
                {isLoading ? (
                    <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                    buttonIcon()
                )}
            </View>
            <Text style={[styles.buttonText, { color: textColor() }]}>
                {buttonText()}
            </Text>
            <View style={styles.spacer} />
        </TouchableOpacity>
    );
};

export default SocialLoginButton;

const styles = StyleSheet.create({
    container: {
        width: "100%",
        height: 56,
        borderRadius: 12,
        flexDirection: "row",
        alignItems: "center",
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    googleButton: {
        backgroundColor: "#FFFFFF",
    },
    facebookButton: {
        backgroundColor: "#FFFFFF",
    },
    appleButton: {
        backgroundColor: "#FFFFFF",
    },
    iconContainer: {
        width: 50,
        alignItems: "center",
        justifyContent: "center",
    },
    buttonText: {
        fontSize: 16,
        fontWeight: "600",
    },
    spacer: {
        width: 50,
    },
});
