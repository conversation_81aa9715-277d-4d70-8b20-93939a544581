import Colors from "@/constants/colors";
import { usePaperStore } from "@/store/paperStore";
import { MaterialIcons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { Image } from "expo-image";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import React from "react";
import {
    Linking,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

export default function PaperDetailScreen() {
    const { id } = useLocalSearchParams();
    const insets = useSafeAreaInsets();
    const router = useRouter();
    const { papers, savedPapers, savePaper, removeSavedPaper } =
        usePaperStore();

    const paper = [...papers, ...savedPapers].find((p) => p.id === id);
    const isSaved = savedPapers.some((p) => p.id === id);

    if (!paper) {
        return (
            <View style={styles.notFoundContainer}>
                <Text style={styles.notFoundText}>Paper not found</Text>
                <TouchableOpacity
                    style={styles.backButton}
                    onPress={() => router.back()}
                >
                    <Text style={styles.backButtonText}>Go Back</Text>
                </TouchableOpacity>
            </View>
        );
    }

    const handleToggleSave = () => {
        if (Platform.OS !== "web") {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        }

        if (isSaved) {
            removeSavedPaper(paper.id);
        } else {
            savePaper(paper);
        }
    };

    const handleOpenDOI = () => {
        if (paper.doi) {
            const url = paper.doi.startsWith("http")
                ? paper.doi
                : `https://doi.org/${paper.doi}`;
            Linking.openURL(url);
        }
    };

    return (
        <>
            <Stack.Screen
                options={{
                    title: "",
                    headerShown: true,
                    headerTransparent: true,
                    headerLeft: () => (
                        <TouchableOpacity
                            style={styles.headerButton}
                            onPress={() => router.back()}
                        >
                            <MaterialIcons
                                name="arrow-left"
                                size={24}
                                color="white"
                            />
                        </TouchableOpacity>
                    ),
                    headerRight: () => (
                        <TouchableOpacity
                            style={styles.headerButton}
                            onPress={handleToggleSave}
                        >
                            {isSaved ? (
                                <MaterialIcons
                                    name="bookmark-added"
                                    size={24}
                                    color="white"
                                />
                            ) : (
                                <MaterialIcons
                                    name="bookmark-remove"
                                    size={24}
                                    color="white"
                                />
                            )}
                        </TouchableOpacity>
                    ),
                }}
            />

            <ScrollView
                style={styles.container}
                contentContainerStyle={{ paddingBottom: insets.bottom + 20 }}
            >
                <View style={styles.imageContainer}>
                    <Image
                        source={{
                            uri:
                                paper.imageUrl ||
                                "https://images.unsplash.com/photo-1532153975070-2e9ab71f1b14?q=80&w=1770&auto=format&fit=crop",
                        }}
                        style={styles.image}
                        contentFit="cover"
                    />
                    <View style={styles.overlay} />
                </View>

                <View style={styles.contentContainer}>
                    <Text style={styles.title}>{paper.title}</Text>

                    <View style={styles.metaContainer}>
                        <Text style={styles.journal}>
                            {paper.journal} • {paper.year}
                        </Text>

                        {paper.citationCount && (
                            <Text style={styles.citations}>
                                {paper.citationCount.toLocaleString()} citations
                            </Text>
                        )}
                    </View>

                    <View style={styles.authorSection}>
                        <Text style={styles.sectionTitle}>Authors</Text>
                        {paper.authors.map((author, index) => (
                            <Text key={index} style={styles.authorText}>
                                {author.name}
                                {author.institution
                                    ? ` • ${author.institution}`
                                    : ""}
                            </Text>
                        ))}
                    </View>

                    <View style={styles.tagsContainer}>
                        {paper.tags.map((tag, index) => (
                            <View key={index} style={styles.tag}>
                                <Text style={styles.tagText}>{tag}</Text>
                            </View>
                        ))}
                    </View>

                    <View style={styles.abstractSection}>
                        <Text style={styles.sectionTitle}>Abstract</Text>
                        <Text style={styles.abstractText}>
                            {paper.abstract}
                        </Text>
                    </View>

                    {paper.doi && (
                        <TouchableOpacity
                            style={styles.doiButton}
                            onPress={handleOpenDOI}
                        >
                            <Text style={styles.doiButtonText}>
                                View Original Paper
                            </Text>
                            <MaterialIcons
                                name="open-in-new"
                                size={18}
                                color={Colors.primary}
                            />
                        </TouchableOpacity>
                    )}

                    <TouchableOpacity
                        style={[
                            styles.saveButton,
                            isSaved ? styles.savedButton : styles.unsavedButton,
                        ]}
                        onPress={handleToggleSave}
                    >
                        {isSaved ? (
                            <>
                                <MaterialIcons
                                    name="bookmark-added"
                                    size={20}
                                    color="white"
                                />
                                <Text style={styles.saveButtonText}>
                                    Saved to Library
                                </Text>
                            </>
                        ) : (
                            <>
                                <MaterialIcons
                                    name="bookmark-remove"
                                    size={20}
                                    color="white"
                                />
                                <Text style={styles.saveButtonText}>
                                    Save to Library
                                </Text>
                            </>
                        )}
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    notFoundContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        padding: 20,
    },
    notFoundText: {
        fontSize: 18,
        color: Colors.text,
        marginBottom: 20,
    },
    backButton: {
        padding: 12,
        backgroundColor: Colors.primary,
        borderRadius: 8,
    },
    backButtonText: {
        color: "white",
        fontWeight: "bold",
    },
    headerButton: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: "rgba(0, 0, 0, 0.3)",
        justifyContent: "center",
        alignItems: "center",
    },
    imageContainer: {
        height: 250,
        width: "100%",
        position: "relative",
    },
    image: {
        width: "100%",
        height: "100%",
    },
    overlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: "rgba(0, 0, 0, 0.3)",
    },
    contentContainer: {
        padding: 20,
        marginTop: -30,
        backgroundColor: Colors.background,
        borderTopLeftRadius: 30,
        borderTopRightRadius: 30,
    },
    title: {
        fontSize: 24,
        fontWeight: "bold",
        color: Colors.text,
        marginBottom: 12,
    },
    metaContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginBottom: 20,
    },
    journal: {
        fontSize: 16,
        color: Colors.textSecondary,
        flex: 1,
    },
    citations: {
        fontSize: 16,
        color: Colors.primary,
        fontWeight: "500",
    },
    authorSection: {
        marginBottom: 20,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: "bold",
        color: Colors.text,
        marginBottom: 8,
    },
    authorText: {
        fontSize: 15,
        color: Colors.textSecondary,
        marginBottom: 4,
    },
    tagsContainer: {
        flexDirection: "row",
        flexWrap: "wrap",
        gap: 8,
        marginBottom: 20,
    },
    tag: {
        backgroundColor: Colors.card,
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        borderWidth: 1,
        borderColor: Colors.border,
    },
    tagText: {
        fontSize: 14,
        color: Colors.textSecondary,
    },
    abstractSection: {
        marginBottom: 24,
    },
    abstractText: {
        fontSize: 16,
        lineHeight: 24,
        color: Colors.text,
    },
    doiButton: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        padding: 16,
        backgroundColor: Colors.card,
        borderRadius: 12,
        marginBottom: 16,
        borderWidth: 1,
        borderColor: Colors.border,
    },
    doiButtonText: {
        fontSize: 16,
        color: Colors.primary,
        fontWeight: "500",
        marginRight: 8,
    },
    saveButton: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        padding: 16,
        borderRadius: 12,
        marginTop: 8,
    },
    savedButton: {
        backgroundColor: Colors.success,
    },
    unsavedButton: {
        backgroundColor: Colors.primary,
    },
    saveButtonText: {
        fontSize: 16,
        color: "white",
        fontWeight: "bold",
        marginLeft: 8,
    },
});
