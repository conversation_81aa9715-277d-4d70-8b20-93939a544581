import { Ionicons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface ProgressBarProps {
    currentStep: number;
    totalSteps: number;
    title: string;
    onBack?: () => void;
    showBackButton?: boolean;
}

export default function ProgressBar({
    currentStep,
    totalSteps,
    title,
    onBack,
    showBackButton = true,
}: ProgressBarProps) {
    return (
        <View style={styles.container}>
            <View style={styles.header}>
                {showBackButton && currentStep > 0 && (
                    <TouchableOpacity
                        style={styles.backButton}
                        onPress={onBack}
                    >
                        <Ionicons name="arrow-back" size={24} color="#111827" />
                    </TouchableOpacity>
                )}
                <Text style={styles.title}>{title}</Text>
                <View style={styles.placeholder} />
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        padding: 16,
        backgroundColor: "#fff",
        borderBottomWidth: 1,
        borderBottomColor: "#E5E7EB",
    },
    header: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
    },
    backButton: {
        padding: 8,
        marginLeft: -8,
    },
    title: {
        fontSize: 20,
        fontWeight: "700",
        color: "#111827",
        flex: 1,
        textAlign: "center",
        marginHorizontal: 16,
    },
    placeholder: {
        width: 40,
    },
});
