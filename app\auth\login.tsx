import SocialLoginButton from "@/components/SocialLoginButton";
import { images } from "@/constants/images";
import { LinearGradient } from "expo-linear-gradient";
import React from "react";
import { Image, Text, TouchableOpacity, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const AcavibeScreen = () => {
    const insets = useSafeAreaInsets();

    return (
        <LinearGradient colors={["#014C8F", "#000930"]} style={{ flex: 1 }}>
            {/* Safe area top padding */}
            <View style={{ flex: 1, paddingTop: insets.top }}>
                <View style={{ flex: 1 }} />

                <View className="flex flex-row justify-center items-center mt-[80px]">
                    <Image
                        source={images.logo}
                        className="w-10 h-10 mr-2"
                        resizeMode="contain"
                    />
                    <Text className="text-4xl font-bold text-white font-inter-bold">
                        acaVibe
                    </Text>
                </View>

                <View style={{ flex: 1 }} />

                {/* Bottom Content */}
                <View
                    className="px-6"
                    style={{ paddingBottom: insets.bottom + 40 }}
                >
                    <Text className="text-xs text-white text-center mb-6 leading-4 opacity-80 font-inter-bold">
                        By tapping &quot;Log in&quot; you agree to our Terms. To
                        learn{"\n"}
                        how we process your data, you can check out our
                        {"\n"}
                        Privacy Policy and Cookie Policy.
                    </Text>

                    <View className="gap-3 mb-6">
                        <SocialLoginButton strategy="google" />
                        <SocialLoginButton strategy="apple" />
                    </View>

                    <TouchableOpacity className="items-center py-3">
                        <Text className="text-sm text-white underline">
                            Having trouble logging in?
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>
        </LinearGradient>
    );
};

export default AcavibeScreen;
