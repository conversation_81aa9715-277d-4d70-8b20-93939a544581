import { StatusBar } from "expo-status-bar";
import React, { useState } from "react";
import { Linking, Text, TextInput, TouchableOpacity, View } from "react-native";

interface ScholarIdInputProps {
    data: any;
    updateData: (key: string, value: any) => void;
    onNext: () => void;
    onBack: () => void;
    isLastStep: boolean;
    isFirstStep: boolean;
}

export default function ScholarIdInput({
    data,
    updateData,
    onNext,
    onBack,
    isFirstStep,
}: ScholarIdInputProps) {
    const [scholarId, setScholarId] = useState(data.scholarId || "");

    const handleNext = () => {
        updateData("scholarId", scholarId);
        onNext();
    };

    const openGoogleScholarHelp = () => {
        Linking.openURL("https://scholar.google.com/");
    };

    return (
        <View className="flex-1 justify-between pt-5">
            <StatusBar style="dark" />
            <View className="flex-1">
                <Text className="text-base font-semibold text-gray-900 mb-3">
                    Scholar ID
                </Text>

                <TextInput
                    className="bg-blue-900 text-white text-base font-semibold px-4 py-4 rounded-xl mb-4"
                    value={scholarId}
                    onChangeText={setScholarId}
                    placeholder="1892812-ASAX"
                    placeholderTextColor="#FFFFFF"
                    autoCapitalize="none"
                    autoCorrect={false}
                />

                <TouchableOpacity
                    className="self-start"
                    onPress={openGoogleScholarHelp}
                >
                    <Text className="text-sm text-gray-500">
                        Don&apos;t know where to find your{" "}
                        <Text className="text-blue-500 underline">
                            Google Scholar ID?
                        </Text>
                    </Text>
                </TouchableOpacity>
            </View>

            <View className="flex-1 " />

            <View className="pb-5">
                <TouchableOpacity
                    className={`bg-blue-900 py-4 rounded-xl items-center mb-3 ${
                        !scholarId.trim() ? "bg-gray-400" : ""
                    }`}
                    onPress={handleNext}
                    disabled={!scholarId.trim()}
                >
                    <Text className="text-white text-base font-semibold">
                        Search
                    </Text>
                </TouchableOpacity>

                {!isFirstStep && (
                    <TouchableOpacity
                        className="bg-gray-200 py-4 rounded-xl items-center"
                        onPress={onBack}
                    >
                        <Text className="text-gray-700 text-base font-semibold">
                            Back
                        </Text>
                    </TouchableOpacity>
                )}
            </View>
        </View>
    );
}
