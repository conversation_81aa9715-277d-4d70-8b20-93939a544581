import "@/global.css";
import { tokenCache } from "@/utils/cache";
import { ClerkLoaded, ClerkProvider } from "@clerk/clerk-expo";
import { useFonts } from "expo-font";
import { SplashScreen, Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useEffect } from "react";

export default function RootLayout() {
    const [fontsLoaded] = useFonts({
        "Inter-Regular": require("../assets/fonts/Inter/static/Inter_18pt-Regular.ttf"),
        "Inter-Medium": require("../assets/fonts/Inter/static/Inter_18pt-Medium.ttf"),
        "Inter-Bold": require("../assets/fonts/Inter/static/Inter_18pt-Bold.ttf"),
        // Add other weights as needed
    });

    useEffect(() => {
        if (fontsLoaded) {
            SplashScreen.hideAsync();
        }
    }, [fontsLoaded]);

    if (!fontsLoaded) {
        return null;
    }

    const publishableKey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY!;

    if (!publishableKey) {
        throw new Error(
            "Add EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY to your .env file"
        );
    }

    return (
        <ClerkProvider publishableKey={publishableKey} tokenCache={tokenCache}>
            <StatusBar style="auto" />
            <ClerkLoaded>
                <Stack screenOptions={{ headerShown: false }}>
                    <Stack.Screen name="index" />
                    <Stack.Screen name="auth" />
                    <Stack.Screen name="(tabs)" />
                </Stack>
            </ClerkLoaded>
        </ClerkProvider>
    );
}
