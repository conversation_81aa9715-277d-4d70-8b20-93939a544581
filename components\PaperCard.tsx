import { Paper } from "@/types/paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import React from "react";
import {
    Dimensions,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from "react-native";

const { width } = Dimensions.get("window");
const CARD_WIDTH = width * 0.9;
const CARD_HEIGHT = width * 1.2; // Fixed aspect ratio for better layout

interface PaperCardProps {
    paper: Paper;
    onSave: () => void;
    onPass: () => void;
    isActive?: boolean;
    stackPosition?: number; // 0 = top, 1 = second, 2 = third
}

export default function PaperCard({
    paper,
    onSave,
    onPass,
    isActive = true,
    stackPosition = 0,
}: PaperCardProps) {
    const router = useRouter();

    const handleViewPaper = () => {
        // Only allow navigation on active (top) card
        if (isActive) {
            router.push(`/paper/${paper.id}`);
        }
    };

    const generateAbstractImage = (paper: Paper) => {
        if (paper.imageUrl) {
            return paper.imageUrl;
        }

        return "https://images.unsplash.com/photo-1532153975070-2e9ab71f1b14?q=80&w=1770&auto=format&fit=crop";
    };

    const getYearColor = (year: number) => {
        const currentYear = new Date().getFullYear();
        const yearDiff = currentYear - year;

        if (yearDiff <= 1) return "#10b981"; // green-500
        if (yearDiff <= 3) return "#f59e0b"; // yellow-500
        return "#9ca3af"; // gray-400
    };

    // Calculate dynamic styles based on stack position
    const getCardStyles = () => {
        const baseStyle = styles.card;

        // Add subtle background blur effect for stacked cards
        if (stackPosition > 0) {
            return [
                baseStyle,
                {
                    backgroundColor: "rgba(255, 255, 255, 0.95)",
                    borderWidth: stackPosition === 1 ? 0.5 : 1,
                    borderColor: `rgba(0, 0, 0, ${0.1 - stackPosition * 0.02})`,
                },
            ];
        }

        return baseStyle;
    };

    return (
        <View style={getCardStyles()}>
            {/* Main Image */}
            <Image
                source={paper.imageUrl ? generateAbstractImage(paper) : ""}
                style={styles.card}
                className="w-full h-full"
                contentFit="cover"
                transition={200}
            />

            <LinearGradient
                colors={[
                    "transparent",
                    stackPosition > 0 ? "rgba(0,0,0,0.6)" : "rgba(0,0,0,0.8)",
                ]}
                className="absolute left-0 right-0 bottom-0 h-3/5 z-10"
            />

            {isActive && (
                <View className="absolute top-4 left-4 right-4 flex-row justify-between items-start z-20">
                    <View className="bg-white/90 px-3 py-1.5 rounded-full">
                        <Text
                            className={`text-sm font-semibold`}
                            style={{ color: getYearColor(paper.year) }}
                        >
                            {paper.year}
                        </Text>
                    </View>

                    <TouchableOpacity
                        className="w-10 h-10 rounded-full bg-white/90 justify-center items-center"
                        onPress={handleViewPaper}
                    >
                        <MaterialCommunityIcons
                            name="open-in-new"
                            size={18}
                            color="#374151"
                        />
                    </TouchableOpacity>
                </View>
            )}

            <View className="absolute bottom-0 left-0 right-0 p-5 z-20">
                <Text
                    className="text-2xl font-bold text-white mb-2 leading-7"
                    numberOfLines={stackPosition > 0 ? 2 : 3}
                    style={{
                        opacity: stackPosition > 0 ? 0.9 : 1,
                        fontSize: stackPosition > 0 ? 20 : 24,
                    }}
                >
                    {paper.title}
                </Text>

                {stackPosition === 0 && (
                    <View className="gap-2">
                        {/* Journal & Year */}
                        <Text className="text-base text-white/90 font-medium">
                            {paper.journal} • {paper.year}
                        </Text>

                        {/* Authors */}
                        <View className="flex-row items-center">
                            <MaterialCommunityIcons
                                name="account-multiple"
                                size={14}
                                color="rgba(255,255,255,0.8)"
                            />
                            <Text
                                className="text-sm text-white/80 ml-2 flex-1"
                                numberOfLines={1}
                            >
                                {paper.authors
                                    .map((author) => author.name)
                                    .join(", ")}
                            </Text>
                        </View>

                        {/* Institution */}
                        {paper.authors[0]?.institution && (
                            <Text className="text-sm text-white/70 italic">
                                {paper.authors[0].institution}
                            </Text>
                        )}

                        {/* Tags */}
                        <View className="flex-row flex-wrap gap-2 mt-1">
                            {paper.tags.slice(0, 4).map((tag, index) => (
                                <View
                                    key={index}
                                    className="bg-white/20 px-2.5 py-1 rounded-2xl"
                                >
                                    <Text className="text-xs text-white font-medium">
                                        {tag}
                                    </Text>
                                </View>
                            ))}
                            {paper.tags.length > 4 && (
                                <View className="bg-white/20 px-2.5 py-1 rounded-2xl">
                                    <Text className="text-xs text-white font-medium">
                                        +{paper.tags.length - 4}
                                    </Text>
                                </View>
                            )}
                        </View>

                        {/* Citation Count */}
                        {paper.citationCount !== undefined && (
                            <View className="flex-row items-center">
                                <MaterialCommunityIcons
                                    name="book-open-page-variant"
                                    size={14}
                                    color="rgba(255,255,255,0.7)"
                                />
                                <Text className="text-sm text-white/70 ml-1 font-medium">
                                    {paper.citationCount.toLocaleString()}{" "}
                                    citations
                                </Text>
                            </View>
                        )}
                    </View>
                )}

                {/* Simplified content for background cards */}
                {stackPosition > 0 && (
                    <View className="gap-1">
                        <Text className="text-sm text-white/80 font-medium">
                            {paper.journal} • {paper.year}
                        </Text>

                        {/* Show only first author for background cards */}
                        <Text className="text-xs text-white/70">
                            {paper.authors[0]?.name}
                            {paper.authors.length > 1 &&
                                ` +${paper.authors.length - 1} more`}
                        </Text>
                    </View>
                )}
            </View>

            {/* Action Buttons - Only show on active (top) card */}
            {isActive && (
                <View className="absolute bottom-5 left-0 right-0 flex-row justify-center gap-6 z-20">
                    <TouchableOpacity
                        className="w-14 h-14 rounded-full bg-red-600 justify-center items-center shadow-lg active:opacity-80"
                        onPress={onPass}
                    >
                        <MaterialCommunityIcons
                            name="close"
                            size={28}
                            color="white"
                        />
                    </TouchableOpacity>

                    <TouchableOpacity
                        className="w-14 h-14 rounded-full bg-blue-600 justify-center items-center shadow-lg active:opacity-80"
                        onPress={onSave}
                    >
                        <MaterialCommunityIcons
                            name="bookmark-plus"
                            size={28}
                            color="white"
                        />
                    </TouchableOpacity>
                </View>
            )}

            {/* Subtle indicator for stacked cards */}
            {stackPosition > 0 && (
                <View className="absolute top-2 left-1/2 transform -translate-x-1/2 z-30">
                    <View
                        className="w-8 h-1 rounded-full"
                        style={{
                            backgroundColor: `rgba(255, 255, 255, ${
                                0.4 - stackPosition * 0.1
                            })`,
                        }}
                    />
                </View>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    card: {
        width: CARD_WIDTH,
        height: CARD_HEIGHT,
        borderRadius: 20,
        backgroundColor: "white",
        overflow: "hidden",
        // Enhanced shadow system for better stacking effect
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
        elevation: 8, // Android shadow
    },
});
