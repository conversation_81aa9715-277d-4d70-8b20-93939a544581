import { papers as mockPapers } from "@/mocks/papers";
import { Paper } from "@/types/paper";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

interface PaperState {
    papers: Paper[];
    savedPapers: Paper[];
    rejectedPapers: string[];
    savePaper: (paper: Paper) => void;
    rejectPaper: (paperId: string) => void;
    removeSavedPaper: (paperId: string) => void;
    resetSwipedPapers: () => void;
    resetAllData: () => void;
    getFilteredPapers: () => Paper[];
}

export const usePaperStore = create<PaperState>()(
    persist(
        (set, get) => ({
            papers: mockPapers,
            savedPapers: [],
            rejectedPapers: [],

            savePaper: (paper: Paper) => {
                set((state) => {
                    // Check if paper is already saved to prevent duplicates
                    const isAlreadySaved = state.savedPapers.some(
                        (saved) => saved.id === paper.id
                    );

                    if (isAlreadySaved) {
                        return state; // Return unchanged state
                    }

                    const newSavedPapers = [...state.savedPapers, paper];

                    return {
                        savedPapers: newSavedPapers,
                    };
                });
            },

            rejectPaper: (paperId: string) => {
                set((state) => ({
                    rejectedPapers: [...state.rejectedPapers, paperId],
                }));
            },

            removeSavedPaper: (paperId: string) => {
                set((state) => ({
                    savedPapers: state.savedPapers.filter(
                        (paper) => paper.id !== paperId
                    ),
                }));
            },

            resetSwipedPapers: () => {
                set({
                    rejectedPapers: [],
                });
            },

            resetAllData: () => {
                set({
                    savedPapers: [],
                    rejectedPapers: [],
                });
            },

            getFilteredPapers: () => {
                const { papers, savedPapers, rejectedPapers } = get();

                const filtered = papers.filter(
                    (paper) =>
                        !savedPapers.some((saved) => saved.id === paper.id) &&
                        !rejectedPapers.includes(paper.id)
                );

                return filtered;
            },
        }),
        {
            name: "paper-storage",
            storage: createJSONStorage(() => AsyncStorage),
            partialize: (state) => ({
                savedPapers: state.savedPapers,
                rejectedPapers: state.rejectedPapers,
            }),
        }
    )
);
