import { useAuth, useUser } from "@clerk/clerk-expo";
import { useRouter } from "expo-router";
import React, { useEffect } from "react";
import { ActivityIndicator, View } from "react-native";

const InitialRoute = () => {
    const { isSignedIn, isLoaded } = useAuth();
    const { user } = useUser();
    const router = useRouter();

    useEffect(() => {
        if (!isLoaded) return;

        if (!isSignedIn) {
            router.replace("/auth/onboarding");
        } else if (user?.unsafeMetadata?.onboardingCompleted !== true) {
            router.replace("/auth/onboarding");
        } else {
            router.replace("/(tabs)/discover");
        }
    }, [isSignedIn, isLoaded, user, router]);

    return (
        <View className="flex-1 justify-center items-center bg-white">
            <ActivityIndicator size="large" color="#000000" />
        </View>
    );
};

export default InitialRoute;
