import { StatusBar } from "expo-status-bar";
import React, { useState } from "react";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";

interface InterestSelectionProps {
    data: any;
    updateData: (key: string, value: any) => void;
    onNext: () => void;
    onBack: () => void;
    onSubmit: () => void;
    isLastStep: boolean;
    isFirstStep: boolean;
}

const availableTopics = [
    "Image Processing",
    "AI",
    "Intelligent Lighting",
    "SaaS",
    "Discussion",
    "Classification",
    "Mastra Agent",
    "Workflow",
    "Graphic Design",
    "Paper",
];

export default function InterestSelection({
    data,
    updateData,
    onNext,
    onBack,
    onSubmit,
    isLastStep,
}: InterestSelectionProps) {
    const [selectedInterests, setSelectedInterests] = useState<string[]>(
        data.selectedInterests || []
    );

    const toggleInterest = (interest: string) => {
        setSelectedInterests((prev) => {
            if (prev.includes(interest)) {
                return prev.filter((item) => item !== interest);
            } else {
                return [...prev, interest];
            }
        });
    };

    const handleConfirm = () => {
        updateData("selectedInterests", selectedInterests);
        if (isLastStep) {
            onSubmit();
        } else {
            onNext();
        }
    };

    return (
        <View className="flex-1 justify-between pt-5">
            <StatusBar style="dark" />
            <View className="flex-1">
                <View className="flex-row justify-end mb-5">
                    <TouchableOpacity className="p-2">
                        <Text className="text-base text-gray-500">Skip</Text>
                    </TouchableOpacity>
                </View>

                <Text className="text-2xl font-bold text-gray-900 mb-3">
                    What topics are you interested in?
                </Text>
                <Text className="text-base text-gray-500 leading-6 mb-8">
                    Add up to 10 interests to your profile to help you find
                    people who share the things you love.
                </Text>

                <ScrollView
                    className="flex-1"
                    showsVerticalScrollIndicator={false}
                >
                    <View className="flex-row flex-wrap gap-3">
                        {availableTopics.map((topic, index) => {
                            const isSelected =
                                selectedInterests.includes(topic);
                            return (
                                <TouchableOpacity
                                    key={index}
                                    className={`px-4 py-2.5 rounded-full border mb-2 ${
                                        isSelected
                                            ? "bg-blue-900 border-blue-900"
                                            : "bg-gray-50 border-gray-200"
                                    }`}
                                    onPress={() => toggleInterest(topic)}
                                >
                                    <Text
                                        className={`text-sm font-medium ${
                                            isSelected
                                                ? "text-white"
                                                : "text-gray-700"
                                        }`}
                                    >
                                        {isSelected ? "✕" : "+"} {topic}
                                    </Text>
                                </TouchableOpacity>
                            );
                        })}
                    </View>
                </ScrollView>
            </View>

            <View className="py-5 ">
                <TouchableOpacity
                    className={`py-4 rounded-xl items-center mb-3 ${
                        selectedInterests.length === 0
                            ? "bg-gray-400"
                            : "bg-blue-900"
                    }`}
                    onPress={handleConfirm}
                    disabled={selectedInterests.length === 0}
                >
                    <Text className="text-white text-base font-semibold">
                        Confirm
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    className="bg-gray-200 py-4 rounded-xl items-center"
                    onPress={onBack}
                >
                    <Text className="text-gray-700 text-base font-semibold">
                        Back
                    </Text>
                </TouchableOpacity>
            </View>
        </View>
    );
}
