import { Paper } from "@/types/paper";
import { MaterialIcons } from "@expo/vector-icons";
import { Image } from "expo-image";
import React from "react";
import { Pressable, Text, View } from "react-native";

interface PaperListItemProps {
    paper: Paper;
    onPress: () => void;
    onRemove?: () => void;
}

export default function PaperListItem({
    paper,
    onPress,
    onRemove,
}: PaperListItemProps) {
    return (
        <Pressable
            className="bg-white rounded-2xl mb-4 p-4 shadow-lg shadow-black/5 border border-gray-100/50 active:opacity-95 active:scale-[0.98] transition-all"
            onPress={onPress}
            style={{
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.08,
                shadowRadius: 12,
                elevation: 8,
            }}
        >
            <View className="flex-row">
                <View className="relative">
                    <Image
                        source={
                            paper.imageUrl ||
                            "https://images.unsplash.com/photo-1532153975070-2e9ab71f1b14?q=80&w=1770&auto=format&fit=crop"
                        }
                        className="rounded-xl"
                        style={{ width: 110, height: 100, borderRadius: 12 }}
                        contentFit="cover"
                    />
                    {/* Gradient overlay for better text contrast */}
                    <View className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl" />
                </View>

                <View className="flex-1 ml-4 justify-between">
                    {/* Title with enhanced typography */}
                    <Text
                        className="text-lg font-bold text-slate-900 leading-tight mb-2"
                        numberOfLines={2}
                        style={{ letterSpacing: -0.3 }}
                    >
                        {paper.title}
                    </Text>

                    {/* Authors with subtle styling */}
                    <Text
                        className="text-sm text-slate-600 mb-3 font-medium"
                        numberOfLines={1}
                    >
                        {paper.authors.map((author) => author.name).join(", ")}
                    </Text>

                    {/* Journal and year with improved spacing */}
                    <View className="flex-row justify-between items-center mb-3">
                        <Text
                            className="text-xs text-slate-500 flex-1 font-medium"
                            numberOfLines={1}
                        >
                            {paper.journal} • {paper.year}
                        </Text>

                        {paper.citationCount && (
                            <View className="bg-blue-50 px-2.5 py-1 rounded-full">
                                <Text className="text-xs text-blue-700 font-semibold">
                                    {paper.citationCount.toLocaleString()}
                                </Text>
                            </View>
                        )}
                    </View>

                    {/* Enhanced tags with modern styling */}
                    <View className="flex-row gap-2 flex-wrap">
                        {paper.tags.slice(0, 3).map((tag, index) => (
                            <View
                                key={index}
                                className="bg-gradient-to-r from-slate-100 to-slate-50 px-3 py-1.5 rounded-full border border-slate-200/50"
                            >
                                <Text className="text-xs text-slate-700 font-medium">
                                    {tag}
                                </Text>
                            </View>
                        ))}
                        {paper.tags.length > 3 && (
                            <View className="bg-slate-100 px-2.5 py-1.5 rounded-full">
                                <Text className="text-xs text-slate-500 font-medium">
                                    +{paper.tags.length - 3}
                                </Text>
                            </View>
                        )}
                    </View>
                </View>
            </View>

            {onRemove && (
                <Pressable
                    className="ml-2 mt-3 p-2.5 justify-center items-center active:opacity-70 active:scale-95"
                    onPress={onRemove}
                    hitSlop={12}
                    style={{
                        backgroundColor: "rgba(239, 68, 68, 0.08)",
                        borderRadius: 12,
                    }}
                >
                    <MaterialIcons
                        name="bookmark-remove"
                        size={20}
                        color="#ef4444"
                    />
                </Pressable>
            )}
        </Pressable>
    );
}
