import { useAuth, useUser } from "@clerk/clerk-expo";
import { Stack, usePathname, useRouter } from "expo-router";
import React, { useEffect } from "react";
import { ActivityIndicator, View } from "react-native";

const AuthLayout = () => {
    const { user, isLoaded: userLoaded } = useUser();
    const router = useRouter();
    const pathName = usePathname();
    const { isSignedIn, isLoaded: authLoaded } = useAuth();

    useEffect(() => {
        if (!authLoaded || !userLoaded) return;

        if (isSignedIn && user?.unsafeMetadata?.onboardingCompleted !== true) {
            if (pathName !== "/auth/onboarding") {
                router.replace("/auth/onboarding");
            }
        } else if (
            isSignedIn &&
            user?.unsafeMetadata?.onboardingCompleted === true
        ) {
            router.replace("/(tabs)/discover");
        }
    }, [isSignedIn, user, pathName, router, authLoaded, userLoaded]);

    if (!authLoaded || !userLoaded) {
        return (
            <View className="flex-1 justify-center items-center bg-white">
                <ActivityIndicator size="large" color="#000000" />
            </View>
        );
    }

    return (
        <Stack>
            <Stack.Screen
                name="login"
                options={{
                    headerShown: false,
                }}
            />
            <Stack.Screen
                name="onboarding"
                options={{
                    headerShown: false,
                }}
            />
        </Stack>
    );
};

export default AuthLayout;
