import { Paper } from "@/types/paper";

export const papers: Paper[] = [
    {
        id: "1",
        title: "Attention Is All You Need",
        abstract:
            "The dominant sequence transduction models are based on complex recurrent or convolutional neural networks that include an encoder and a decoder. The best performing models also connect the encoder and decoder through an attention mechanism. We propose a new simple network architecture, the Transformer, based solely on attention mechanisms, dispensing with recurrence and convolutions entirely.",
        authors: [
            { id: "1", name: "<PERSON><PERSON>aswan<PERSON>", institution: "Google Brain" },
            { id: "2", name: "<PERSON><PERSON>", institution: "Google Brain" },
            { id: "3", name: "<PERSON><PERSON>", institution: "Google Research" },
        ],
        journal: "Advances in Neural Information Processing Systems",
        year: 2017,
        doi: "10.48550/arXiv.1706.03762",
        tags: ["Deep Learning", "NLP", "Transformers"],
        imageUrl:
            "https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=1965&auto=format&fit=crop",
        citationCount: 45000,
    },
    {
        id: "2",
        title: "BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding",
        abstract:
            "We introduce a new language representation model called BERT, which stands for Bidirectional Encoder Representations from Transformers. Unlike recent language representation models, BERT is designed to pre-train deep bidirectional representations from unlabeled text by jointly conditioning on both left and right context in all layers.",
        authors: [
            {
                id: "4",
                name: "Jacob Devlin",
                institution: "Google AI Language",
            },
            {
                id: "5",
                name: "Ming-Wei Chang",
                institution: "Google AI Language",
            },
            { id: "6", name: "Kenton Lee", institution: "Google AI Language" },
        ],
        journal:
            "North American Chapter of the Association for Computational Linguistics",
        year: 2019,
        doi: "10.48550/arXiv.1810.04805",
        tags: ["Deep Learning", "NLP", "BERT"],
        imageUrl:
            "https://images.unsplash.com/photo-1555952494-efd681c7e3f9?q=80&w=2070&auto=format&fit=crop",
        citationCount: 32000,
    },
    {
        id: "3",
        title: "Deep Residual Learning for Image Recognition",
        abstract:
            "Deeper neural networks are more difficult to train. We present a residual learning framework to ease the training of networks that are substantially deeper than those used previously. We explicitly reformulate the layers as learning residual functions with reference to the layer inputs, instead of learning unreferenced functions.",
        authors: [
            { id: "7", name: "Kaiming He", institution: "Microsoft Research" },
            {
                id: "8",
                name: "Xiangyu Zhang",
                institution: "Microsoft Research",
            },
            {
                id: "9",
                name: "Shaoqing Ren",
                institution: "Microsoft Research",
            },
        ],
        journal: "IEEE Conference on Computer Vision and Pattern Recognition",
        year: 2016,
        doi: "10.48550/arXiv.1512.03385",
        tags: ["Deep Learning", "Computer Vision", "ResNet"],
        imageUrl:
            "https://images.unsplash.com/photo-1507146153580-69a1fe6d8aa1?q=80&w=2070&auto=format&fit=crop",
        citationCount: 88000,
    },
    {
        id: "4",
        title: "Generative Adversarial Networks",
        abstract:
            "We propose a new framework for estimating generative models via an adversarial process, in which we simultaneously train two models: a generative model G that captures the data distribution, and a discriminative model D that estimates the probability that a sample came from the training data rather than G.",
        authors: [
            {
                id: "10",
                name: "Ian J. Goodfellow",
                institution: "University of Montreal",
            },
            {
                id: "11",
                name: "Jean Pouget-Abadie",
                institution: "University of Montreal",
            },
            {
                id: "12",
                name: "Mehdi Mirza",
                institution: "University of Montreal",
            },
        ],
        journal: "Advances in Neural Information Processing Systems",
        year: 2014,
        doi: "10.48550/arXiv.1406.2661",
        tags: ["Deep Learning", "GANs", "Generative Models"],
        imageUrl:
            "https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2070&auto=format&fit=crop",
        citationCount: 30000,
    },
    {
        id: "5",
        title: "Distributed Representations of Words and Phrases and their Compositionality",
        abstract:
            "The recently introduced continuous Skip-gram model is an efficient method for learning high-quality distributed vector representations that capture a large number of precise syntactic and semantic word relationships. In this paper we present several extensions that improve both the quality of the vectors and the training speed.",
        authors: [
            { id: "13", name: "Tomas Mikolov", institution: "Google" },
            { id: "14", name: "Ilya Sutskever", institution: "Google" },
            { id: "15", name: "Kai Chen", institution: "Google" },
        ],
        journal: "Advances in Neural Information Processing Systems",
        year: 2013,
        doi: "10.48550/arXiv.1310.4546",
        tags: ["NLP", "Word Embeddings", "Word2Vec"],
        imageUrl:
            "https://images.unsplash.com/photo-1456513080510-7bf3a84b82f8?q=80&w=1973&auto=format&fit=crop",
        citationCount: 25000,
    },
    {
        id: "6",
        title: "ImageNet Classification with Deep Convolutional Neural Networks",
        abstract:
            "We trained a large, deep convolutional neural network to classify the 1.2 million high-resolution images in the ImageNet LSVRC-2010 contest into the 1000 different classes. On the test data, we achieved top-1 and top-5 error rates of 37.5% and 17.0% which is considerably better than the previous state-of-the-art.",
        authors: [
            {
                id: "16",
                name: "Alex Krizhevsky",
                institution: "University of Toronto",
            },
            {
                id: "17",
                name: "Ilya Sutskever",
                institution: "University of Toronto",
            },
            {
                id: "18",
                name: "Geoffrey E. Hinton",
                institution: "University of Toronto",
            },
        ],
        journal: "Advances in Neural Information Processing Systems",
        year: 2012,
        doi: "10.1145/3065386",
        tags: ["Deep Learning", "Computer Vision", "AlexNet"],
        imageUrl:
            "https://images.unsplash.com/photo-1518770660439-4636190af475?q=80&w=2070&auto=format&fit=crop",
        citationCount: 70000,
    },
];
