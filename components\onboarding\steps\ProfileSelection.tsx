import { StatusBar } from "expo-status-bar";
import React from "react";
import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface ProfileSelectionProps {
    data: any;
    updateData: (key: string, value: any) => void;
    onNext: () => void;
    onBack: () => void;
    isLastStep: boolean;
    isFirstStep: boolean;
}

// Mock profile data - in real app this would come from Google Scholar API
const mockProfile = {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    university: "Pennsylvania State University",
    avatar: "https://media.licdn.com/dms/image/v2/D4D03AQES6p92mxUzfg/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1716424065559?e=1753315200&v=beta&t=yjEU07NkQ_bNlsm1kcmVL_TNj26cyFkWcUquj4K49hM",
    interests: [
        "Image Processing",
        "AI",
        "Intelligent Lighting",
        "SaaS",
        "Ruby-on-Rails",
    ],
};

export default function ProfileSelection({
    data,
    updateData,
    onNext,
    onBack,
}: ProfileSelectionProps) {
    const handleConfirm = () => {
        updateData("profileData", mockProfile);
        onNext();
    };

    return (
        <View style={styles.container}>
            <StatusBar style="dark" />
            <View style={styles.content}>
                <View style={styles.profileContainer}>
                    <View style={styles.avatarContainer}>
                        <Image
                            source={{ uri: mockProfile.avatar }}
                            style={styles.avatar}
                        />
                    </View>

                    <Text style={styles.name}>{mockProfile.name}</Text>
                    <Text style={styles.university}>
                        {mockProfile.university}
                    </Text>

                    <View style={styles.interestsContainer}>
                        {mockProfile.interests.map((interest, index) => (
                            <View key={index} style={styles.interestTag}>
                                <Text style={styles.interestText}>
                                    ✕ {interest}
                                </Text>
                            </View>
                        ))}
                    </View>
                </View>
            </View>

            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={styles.confirmButton}
                    onPress={handleConfirm}
                >
                    <Text style={styles.confirmButtonText}>Confirm</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.backButton} onPress={onBack}>
                    <Text style={styles.backButtonText}>Back</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: "space-between",
        paddingTop: 20,
    },
    content: {
        flex: 1,
    },
    profileContainer: {
        alignItems: "center",
    },
    avatarContainer: {
        width: 120,
        height: 120,
        borderRadius: 60,
        borderWidth: 4,
        borderColor: "#004D90",
        padding: 4,
        marginBottom: 24,
        overflow: "hidden",
    },
    avatar: {
        width: "100%",
        height: "100%",
        borderRadius: 60,
    },
    name: {
        fontSize: 24,
        fontWeight: "700",
        color: "#111827",
        marginBottom: 8,
        textAlign: "center",
    },
    university: {
        fontSize: 16,
        color: "#6B7280",
        marginBottom: 32,
        textAlign: "center",
    },
    interestsContainer: {
        flexDirection: "row",
        flexWrap: "wrap",
        justifyContent: "center",
        gap: 8,
    },
    interestTag: {
        backgroundColor: "#1E3A8A",
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        marginBottom: 8,
    },
    interestText: {
        color: "#FFFFFF",
        fontSize: 14,
        fontWeight: "500",
    },
    buttonContainer: {
        paddingBottom: 20,
    },
    confirmButton: {
        backgroundColor: "#1E3A8A",
        paddingVertical: 16,
        borderRadius: 12,
        alignItems: "center",
        marginBottom: 12,
    },
    confirmButtonText: {
        color: "#FFFFFF",
        fontSize: 16,
        fontWeight: "600",
    },
    backButton: {
        backgroundColor: "#E5E7EB",
        paddingVertical: 16,
        borderRadius: 12,
        alignItems: "center",
    },
    backButtonText: {
        color: "#374151",
        fontSize: 16,
        fontWeight: "600",
    },
});
