import Colors from "@/constants/colors";
import { usePaperStore } from "@/store/paperStore";
import { MaterialIcons } from "@expo/vector-icons";
import { Image } from "expo-image";
import React from "react";
import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

export default function ProfileScreen() {
    const insets = useSafeAreaInsets();
    const { savedPapers, rejectedPapers, resetSwipedPapers } = usePaperStore();

    const getTopTags = () => {
        const tagCounts: Record<string, number> = {};
        savedPapers.forEach((paper) => {
            paper.tags.forEach((tag) => {
                tagCounts[tag] = (tagCounts[tag] || 0) + 1;
            });
        });

        return Object.entries(tagCounts)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .map(([tag, count]) => ({ tag, count }));
    };

    const topTags = getTopTags();

    return (
        <ScrollView
            style={[styles.container, { paddingTop: insets.top }]}
            contentContainerStyle={styles.contentContainer}
        >
            <View style={styles.profileHeader}>
                <View style={styles.avatarContainer}>
                    <Image
                        source={{
                            uri: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=1887&auto=format&fit=crop",
                        }}
                        style={styles.avatar}
                    />
                </View>
                <Text style={styles.name}>Academic Explorer</Text>
                <Text style={styles.bio}>
                    Exploring the frontiers of research
                </Text>
            </View>

            <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                    <Text style={styles.statValue}>{savedPapers.length}</Text>
                    <Text style={styles.statLabel}>Saved</Text>
                </View>
                <View style={styles.statDivider} />
                <View style={styles.statItem}>
                    <Text style={styles.statValue}>
                        {rejectedPapers.length}
                    </Text>
                    <Text style={styles.statLabel}>Skipped</Text>
                </View>
                <View style={styles.statDivider} />
                <View style={styles.statItem}>
                    <Text style={styles.statValue}>
                        {savedPapers.length + rejectedPapers.length}
                    </Text>
                    <Text style={styles.statLabel}>Total</Text>
                </View>
            </View>

            {topTags.length > 0 && (
                <View style={styles.section}>
                    <Text style={styles.sectionTitle}>
                        Your Research Interests
                    </Text>
                    <View style={styles.tagsContainer}>
                        {topTags.map((item, index) => (
                            <View key={index} style={styles.tagItem}>
                                <Text style={styles.tagText}>{item.tag}</Text>
                                <Text style={styles.tagCount}>
                                    {item.count}
                                </Text>
                            </View>
                        ))}
                    </View>
                </View>
            )}

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Actions</Text>

                <TouchableOpacity
                    style={styles.actionButton}
                    onPress={resetSwipedPapers}
                >
                    <MaterialIcons
                        name="refresh"
                        size={20}
                        color={Colors.primary}
                    />
                    <Text style={styles.actionText}>Reset Skipped Papers</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.actionButton}>
                    <MaterialIcons
                        name="settings"
                        size={20}
                        color={Colors.primary}
                    />
                    <Text style={styles.actionText}>Settings</Text>
                </TouchableOpacity>
            </View>

            <View style={styles.footer}>
                <Text style={styles.footerText}>PaperSwipe v1.0</Text>
            </View>
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    contentContainer: {
        padding: 20,
        paddingBottom: 40,
    },
    profileHeader: {
        alignItems: "center",
        marginBottom: 30,
    },
    avatarContainer: {
        width: 100,
        height: 100,
        borderRadius: 50,
        overflow: "hidden",
        marginBottom: 16,
        shadowColor: Colors.shadow,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 6,
        elevation: 8,
    },
    avatar: {
        width: "100%",
        height: "100%",
    },
    name: {
        fontSize: 24,
        fontWeight: "bold",
        color: Colors.text,
        marginBottom: 4,
    },
    bio: {
        fontSize: 16,
        color: Colors.textSecondary,
    },
    statsContainer: {
        flexDirection: "row",
        backgroundColor: Colors.card,
        borderRadius: 12,
        padding: 16,
        marginBottom: 24,
        shadowColor: Colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
    },
    statItem: {
        flex: 1,
        alignItems: "center",
    },
    statValue: {
        fontSize: 20,
        fontWeight: "bold",
        color: Colors.text,
        marginBottom: 4,
    },
    statLabel: {
        fontSize: 14,
        color: Colors.textSecondary,
    },
    statDivider: {
        width: 1,
        backgroundColor: Colors.border,
        marginHorizontal: 10,
    },
    section: {
        marginBottom: 24,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: "bold",
        color: Colors.text,
        marginBottom: 12,
    },
    tagsContainer: {
        flexDirection: "row",
        flexWrap: "wrap",
        gap: 8,
    },
    tagItem: {
        flexDirection: "row",
        backgroundColor: Colors.card,
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 20,
        alignItems: "center",
    },
    tagText: {
        fontSize: 14,
        color: Colors.text,
        marginRight: 6,
    },
    tagCount: {
        fontSize: 12,
        color: Colors.primary,
        fontWeight: "bold",
    },
    actionButton: {
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: Colors.card,
        padding: 16,
        borderRadius: 12,
        marginBottom: 12,
    },
    actionText: {
        fontSize: 16,
        color: Colors.text,
        marginLeft: 12,
    },
    footer: {
        alignItems: "center",
        marginTop: 20,
    },
    footerText: {
        fontSize: 14,
        color: Colors.textSecondary,
    },
});
